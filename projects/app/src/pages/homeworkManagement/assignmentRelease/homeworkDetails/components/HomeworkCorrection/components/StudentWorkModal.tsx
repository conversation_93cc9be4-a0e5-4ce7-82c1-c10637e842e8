import React, { useState, useEffect } from 'react';
import { Box, Flex, Text, Button } from '@chakra-ui/react';
import { message } from 'antd';
import MyModal from '@/components/MyModal';
import SvgIcon from '@/components/SvgIcon';
import { vwDims } from '@/utils/chakra';
import { uploadFileRecognizeStudentCode } from '@/api/file';

// 添加CSS动画样式
const spinKeyframes = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

// 将样式注入到页面
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = spinKeyframes;
  document.head.appendChild(style);
}

// 学生匹配数据结构
interface StudentMatch {
  studentId: number;
  studentName: string;
  studentCode: string;
  avatarUrl?: string;
  content: string;
  status: 'matched' | 'unmatched';
  action: 'edit' | 'add';
  progress?: number;
}

// 上传文件数据结构
interface UploadedFile {
  id: string;
  name: string;
  url: string;
  fileKey?: string;
  status: 'uploading' | 'done' | 'error' | 'unmatched' | 'matched' | 'processing';
  type?: string;
  size: number;
  studentCode?: string;
  matchedStudent?: StudentMatch;
}

// 组件Props接口
interface StudentWorkModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (student: StudentMatch, files: UploadedFile[]) => void;
  student: StudentMatch | null;
  files?: UploadedFile[]; // 学生匹配的文件列表
  onFileUpload?: (file: UploadedFile, student: StudentMatch) => void; // 文件上传回调
}

const StudentWorkModal: React.FC<StudentWorkModalProps> = ({
  isOpen,
  onClose,
  onSave,
  student,
  files = [],
  onFileUpload
}) => {
  const [studentWorkFiles, setStudentWorkFiles] = useState<UploadedFile[]>([]);
  const [selectedFileIndex, setSelectedFileIndex] = useState(0);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  // 文件预览组件 - 优化比较函数
  const FilePreview: React.FC<{ file: UploadedFile }> = React.memo(({ file }) => {
    // 使用 file.url 的存在性来初始化加载状态，避免useEffect循环
    const [imageError, setImageError] = useState(false);
    const [imageLoading, setImageLoading] = useState(true);

    // 使用 ref 来跟踪当前文件，避免无限循环
    const currentFileRef = React.useRef<string>('');

    const isImage = file.type?.startsWith('image/');
    const isPDF = file.type === 'application/pdf';

    // 处理 URL，解决双重编码问题 - 优化为更稳定的实现
    const processedUrl = React.useMemo(() => {
      if (!file.url) return '';

      // 存储原始 URL，避免重复处理
      let result = file.url;

      // 只在包含编码字符时才尝试解码
      if (result.includes('%')) {
        try {
          const firstDecode = decodeURIComponent(result);
          // 如果第一次解码后仍然包含 % 且与原始不同，尝试第二次解码
          if (firstDecode !== result && firstDecode.includes('%')) {
            try {
              result = decodeURIComponent(firstDecode);
            } catch (e2) {
              result = firstDecode;
            }
          } else {
            result = firstDecode;
          }
        } catch (error) {
          console.warn('URL decode failed:', error);
          // 解码失败时保持原始 URL
        }
      }

      return result;
    }, [file.url]);

    // 只在文件真正变化时重置状态
    React.useEffect(() => {
      const fileKey = `${file.id}-${file.url}`;
      if (currentFileRef.current !== fileKey) {
        currentFileRef.current = fileKey;
        if (file.url) {
          setImageLoading(true);
          setImageError(false);
        } else {
          setImageLoading(false);
          setImageError(true);
        }
      }
    }, [file.id, file.url]);

    // 稳定的事件处理器，避免无限循环
    const handleImageLoad = React.useCallback(() => {
      setImageLoading(false);
    }, []);

    const handleImageError = React.useCallback(() => {
      console.error('Image load failed:', file.name, 'URL:', processedUrl);
      setImageError(true);
      setImageLoading(false);
    }, [file.name, processedUrl]);

    // PDF 的事件处理器
    const handlePdfLoad = React.useCallback(() => {
      setImageLoading(false);
    }, []);

    const handlePdfError = React.useCallback(() => {
      setImageError(true);
    }, []);

    // 处理文件状态，即使状态为 error 也尝试显示图片
    const shouldShowImage = isImage && processedUrl && file.status !== 'uploading';
    const shouldShowPDF = isPDF && processedUrl && file.status !== 'uploading';

    if (shouldShowImage && !imageError) {
      return (
        <Box w="100%" h="100%" position="relative" bg="#FAFBFC">
          {imageLoading && (
            <Box
              position="absolute"
              top="0"
              left="0"
              w="100%"
              h="100%"
              display="flex"
              alignItems="center"
              justifyContent="center"
              bg="#FAFBFC"
              flexDirection="column"
            >
              {/* 大尺寸加载动画 */}
              <Box
                w={vwDims(32)}
                h={vwDims(32)}
                border="3px solid #E5E7EB"
                borderTop="3px solid #7D4DFF"
                borderRadius="50%"
                animation="spin 1s linear infinite"
                mb={vwDims(8)}
              />
              <Text fontSize={vwDims(16)} color="#86909C">
                图片加载中...
              </Text>
            </Box>
          )}
          <Box
            as="img"
            src={processedUrl}
            alt={file.name}
            maxW="100%"
            maxH="100%"
            objectFit="contain"
            borderRadius={vwDims(8)}
            onLoad={handleImageLoad}
            onError={handleImageError}
            opacity={imageLoading ? 0 : 1}
            transition="opacity 0.3s ease"
            margin="auto"
          />
        </Box>
      );
    }

    if (shouldShowPDF) {
      return (
        <Box w="100%" h="100%" position="relative" bg="#FAFBFC">
          {/* PDF预览iframe */}
          <Box
            as="iframe"
            src={`${processedUrl}#toolbar=1&navpanes=0&scrollbar=1&page=1&view=FitH`}
            w="100%"
            h="100%"
            border="none"
            borderRadius={vwDims(8)}
            onLoad={handlePdfLoad}
            onError={handlePdfError}
            display={imageLoading ? 'none' : 'block'}
          />

          {/* 加载状态 */}
          {imageLoading && (
            <Box
              position="absolute"
              top="0"
              left="0"
              w="100%"
              h="100%"
              display="flex"
              alignItems="center"
              justifyContent="center"
              bg="#FAFBFC"
              flexDirection="column"
            >
              {/* PDF加载动画 */}
              <Box
                w={vwDims(32)}
                h={vwDims(32)}
                border="3px solid #E5E7EB"
                borderTop="3px solid #7D4DFF"
                borderRadius="50%"
                animation="spin 1s linear infinite"
                mb={vwDims(8)}
              />
              <Text fontSize={vwDims(16)} color="#86909C">
                PDF加载中...
              </Text>
            </Box>
          )}

          {/* 错误状态 */}
          {imageError && (
            <Box w="100%" h="100%" display="flex" alignItems="center" justifyContent="center">
              <Box textAlign="center">
                <SvgIcon name="file" w={vwDims(48)} h={vwDims(48)} />
                <Text fontSize={vwDims(16)} color="#1A1A1A" fontWeight="500" mt={2}>
                  PDF 预览失败
                </Text>
                <Text fontSize={vwDims(14)} color="#86909C">
                  {file.name}
                </Text>
              </Box>
            </Box>
          )}
        </Box>
      );
    }

    // 默认文件显示
    return (
      <Box w="100%" h="100%" display="flex" alignItems="center" justifyContent="center">
        <Box textAlign="center">
          <SvgIcon name="file" w={vwDims(48)} h={vwDims(48)} />
          <Text fontSize={vwDims(16)} color="#1A1A1A" fontWeight="500" mt={2}>
            {isImage ? '图片文件' : '文件'}
          </Text>
          <Text fontSize={vwDims(14)} color="#86909C">
            {imageError ? '图片加载失败' : file.name}
          </Text>
        </Box>
      </Box>
    );
  });

  // 优化 FilePreview 的比较函数，只在关键属性变化时才重新渲染
  const MemoizedFilePreview = React.memo(FilePreview, (prevProps, nextProps) => {
    return (
      prevProps.file.id === nextProps.file.id &&
      prevProps.file.url === nextProps.file.url &&
      prevProps.file.status === nextProps.file.status
    );
  });

  FilePreview.displayName = 'FilePreview';
  // 小尺寸文件预览组件（用于缩略图）
  const SmallFilePreview: React.FC<{ file: UploadedFile }> = React.memo(({ file }) => {
    const [imageError, setImageError] = useState(false);
    const [imageLoading, setImageLoading] = useState(true);

    // 使用 ref 来跟踪当前文件，避免无限循环
    const currentFileRef = React.useRef<string>('');

    const isImage = file.type?.startsWith('image/');
    const isPDF = file.type === 'application/pdf';

    // 处理 URL，解决双重编码问题 - 优化为更稳定的实现
    const processedUrl = React.useMemo(() => {
      if (!file.url) return '';

      // 存储原始 URL，避免重复处理
      let result = file.url;

      // 只在包含编码字符时才尝试解码
      if (result.includes('%')) {
        try {
          const firstDecode = decodeURIComponent(result);
          // 如果第一次解码后仍然包含 % 且与原始不同，尝试第二次解码
          if (firstDecode !== result && firstDecode.includes('%')) {
            try {
              result = decodeURIComponent(firstDecode);
            } catch (e2) {
              result = firstDecode;
            }
          } else {
            result = firstDecode;
          }
        } catch (error) {
          console.warn('Thumbnail URL decode failed:', error);
          // 解码失败时保持原始 URL
        }
      }

      return result;
    }, [file.url]);

    // 只在文件真正变化时重置状态
    React.useEffect(() => {
      const fileKey = `${file.id}-${file.url}`;
      if (currentFileRef.current !== fileKey) {
        currentFileRef.current = fileKey;
        if (file.url) {
          setImageLoading(true);
          setImageError(false);
        } else {
          setImageLoading(false);
          setImageError(true);
        }
      }
    }, [file.id, file.url]);

    // 处理文件状态，即使状态为 error 也尝试显示图片
    const shouldShowImage = isImage && processedUrl && file.status !== 'uploading';
    const shouldShowPDF = isPDF && processedUrl && file.status !== 'uploading';

    // 稳定的事件处理器，避免无限循环
    const handleThumbnailLoad = React.useCallback(() => {
      setImageLoading(false);
    }, []);

    const handleThumbnailError = React.useCallback(() => {
      console.error('Thumbnail load failed:', file.name, 'URL:', processedUrl);
      setImageError(true);
      setImageLoading(false);
    }, [file.name, processedUrl]);

    // PDF 的事件处理器
    const handlePdfLoad = React.useCallback(() => {
      setImageLoading(false);
    }, []);

    const handlePdfError = React.useCallback(() => {
      setImageError(true);
    }, []);

    if (shouldShowImage && !imageError) {
      return (
        <Box w="100%" h="100%" position="relative">
          {imageLoading && (
            <Box
              position="absolute"
              top="0"
              left="0"
              w="100%"
              h="100%"
              display="flex"
              alignItems="center"
              justifyContent="center"
              bg="#F5F5F5"
            >
              <Text fontSize={vwDims(8)} color="#86909C">
                加载中...
              </Text>
            </Box>
          )}
          <Box
            as="img"
            src={processedUrl}
            alt={file.name}
            w="100%"
            h="100%"
            objectFit="cover"
            borderRadius={vwDims(8)}
            onLoad={handleThumbnailLoad}
            onError={handleThumbnailError}
            opacity={imageLoading ? 0 : 1}
            transition="opacity 0.3s ease"
          />
        </Box>
      );
    }

    if (shouldShowPDF) {
      return (
        <Box w="100%" h="100%" position="relative" bg="#F5F5F5" borderRadius={vwDims(8)}>
          {/* PDF预览iframe - 小尺寸 */}
          <Box
            as="iframe"
            src={`${processedUrl}#toolbar=0&navpanes=0&scrollbar=0&page=1&view=FitH`}
            w="100%"
            h="100%"
            border="none"
            borderRadius={vwDims(8)}
            onLoad={handlePdfLoad}
            onError={handlePdfError}
            display={imageLoading ? 'none' : 'block'}
            pointerEvents="none" // 禁用交互，只用于预览
          />

          {/* 加载状态 */}
          {imageLoading && (
            <Box
              position="absolute"
              top="0"
              left="0"
              w="100%"
              h="100%"
              display="flex"
              alignItems="center"
              justifyContent="center"
              bg="#F5F5F5"
              flexDirection="column"
            >
              {/* 小尺寸加载动画 */}
              <Box
                w={vwDims(12)}
                h={vwDims(12)}
                border="2px solid #E5E7EB"
                borderTop="2px solid #7D4DFF"
                borderRadius="50%"
                animation="spin 1s linear infinite"
                mb={vwDims(2)}
              />
              <Text fontSize={vwDims(8)} color="#86909C">
                加载中...
              </Text>
            </Box>
          )}

          {/* 错误状态 */}
          {imageError && (
            <Box
              w="100%"
              h="100%"
              display="flex"
              alignItems="center"
              justifyContent="center"
              flexDirection="column"
            >
              <SvgIcon name="file" w={vwDims(20)} h={vwDims(20)} />
              <Text fontSize={vwDims(8)} color="#86909C" mt={1}>
                PDF
              </Text>
            </Box>
          )}

          {/* PDF标识 - 悬浮在左上角 */}
          <Box
            position="absolute"
            top={vwDims(4)}
            left={vwDims(4)}
            bg="rgba(0, 0, 0, 0.7)"
            color="white"
            px={vwDims(4)}
            py={vwDims(2)}
            borderRadius={vwDims(2)}
            fontSize={vwDims(8)}
            fontWeight="bold"
            zIndex={2}
          >
            PDF
          </Box>
        </Box>
      );
    }

    // 默认文件图标
    return (
      <Box
        w="100%"
        h="100%"
        display="flex"
        alignItems="center"
        justifyContent="center"
        flexDirection="column"
      >
        <SvgIcon name="file" w={vwDims(20)} h={vwDims(20)} />
        <Text fontSize={vwDims(8)} color="#86909C" mt={1}>
          {isImage ? '图片' : '文件'}
        </Text>
      </Box>
    );
  });

  // 优化 SmallFilePreview 的比较函数
  const MemoizedSmallFilePreview = React.memo(SmallFilePreview, (prevProps, nextProps) => {
    return (
      prevProps.file.id === nextProps.file.id &&
      prevProps.file.url === nextProps.file.url &&
      prevProps.file.status === nextProps.file.status
    );
  });

  SmallFilePreview.displayName = 'SmallFilePreview';
  // 当学生信息或文件列表变化时，初始化文件列表
  useEffect(() => {
    if (student) {
      if (files && files.length > 0) {
        // 使用传入的真实文件数据
        setStudentWorkFiles(files);
      } else {
        // 如果没有文件，初始化为空数组
        setStudentWorkFiles([]);
      }
      setSelectedFileIndex(0);
    }
  }, [student, files]);

  // 关闭弹窗（恢复到原始状态）
  const handleClose = () => {
    // 恢复到原始文件状态，取消所有未保存的更改
    setStudentWorkFiles(files);
    setSelectedFileIndex(0);
    onClose();
  };

  // 添加学生作业文件
  const handleAddStudentWorkFile = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.pdf,.png,.jpg,.jpeg';
    input.multiple = true;
    input.onchange = async (e) => {
      const files = Array.from((e.target as HTMLInputElement).files || []);

      if (files.length === 0) return;

      // 检查当前学生的文件数量是否已达到上限
      if (studentWorkFiles.length >= 3) {
        message.error(`学生 ${student?.studentName} 的文件数量已达上限（3个）`);
        return;
      }

      // 检查上传后是否会超过限制
      if (studentWorkFiles.length + files.length > 3) {
        const allowedCount = 3 - studentWorkFiles.length;
        message.error(`最多只能再上传 ${allowedCount} 个文件，当前选择了 ${files.length} 个文件`);
        return;
      }

      setIsUploading(true);

      for (const file of files) {
        try {
          // 创建FormData
          const formData = new FormData();
          formData.append('file', file);

          // 调用上传API
          const result = await uploadFileRecognizeStudentCode(formData, {
            onUploadProgress: (progressEvent) => {
              console.log('Upload progress:', progressEvent);
            }
          });

          // 创建上传文件对象，直接匹配到当前学生
          const uploadedFile: UploadedFile = {
            id: Date.now().toString() + Math.random().toString(),
            name: file.name,
            url: result.fileUrl,
            fileKey: result.fileKey,
            status: 'matched', // 直接设置为已匹配
            type: file.type,
            size: file.size,
            studentCode: student?.studentCode,
            matchedStudent: student
              ? {
                  studentId: student.studentId,
                  studentName: student.studentName,
                  studentCode: student.studentCode,
                  content: student.content,
                  status: 'matched',
                  action: 'edit',
                  progress: student.progress
                }
              : undefined
          };

          // 添加到本地文件列表
          setStudentWorkFiles((prev) => [...prev, uploadedFile]);

          // 通知父组件添加文件
          if (onFileUpload && student) {
            onFileUpload(uploadedFile, student);
          }

          console.log(`文件上传成功并直接匹配到学生 ${student?.studentName}`);
        } catch (error) {
          console.error('文件上传失败:', error);
          message.error(`文件 ${file.name} 上传失败`);
        }
      }

      setIsUploading(false);
    };
    input.click();
  };

  // 删除学生作业文件（临时删除，需要点击保存才正式删除）
  const handleRemoveStudentWorkFile = (fileId: string) => {
    const fileIndex = studentWorkFiles.findIndex((f) => f.id === fileId);

    // 只更新本地状态，不立即通知父组件
    setStudentWorkFiles((prev) => prev.filter((f) => f.id !== fileId));

    // 调整选中的文件索引
    if (fileIndex === selectedFileIndex && studentWorkFiles.length > 1) {
      setSelectedFileIndex(Math.max(0, selectedFileIndex - 1));
    } else if (fileIndex < selectedFileIndex) {
      setSelectedFileIndex(selectedFileIndex - 1);
    }

    message.info('文件已移除，点击保存后正式删除');
  };

  // 拖拽开始
  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  };

  // 拖拽结束
  const handleDragEnd = () => {
    setDraggedIndex(null);
  };

  // 拖拽悬停
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  // 拖拽释放
  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();

    if (draggedIndex === null || draggedIndex === dropIndex) return;

    const newFiles = [...studentWorkFiles];
    const draggedFile = newFiles[draggedIndex];

    // 移除拖拽的文件
    newFiles.splice(draggedIndex, 1);
    // 在新位置插入文件
    newFiles.splice(dropIndex, 0, draggedFile);

    setStudentWorkFiles(newFiles);

    // 更新选中的文件索引
    if (selectedFileIndex === draggedIndex) {
      setSelectedFileIndex(dropIndex);
    } else if (selectedFileIndex > draggedIndex && selectedFileIndex <= dropIndex) {
      setSelectedFileIndex(selectedFileIndex - 1);
    } else if (selectedFileIndex < draggedIndex && selectedFileIndex >= dropIndex) {
      setSelectedFileIndex(selectedFileIndex + 1);
    }

    setDraggedIndex(null);
  };

  // 保存学生作业
  const handleSave = () => {
    if (!student) return;

    // 根据文件数量更新学生的匹配状态
    const updatedStudent: StudentMatch = {
      ...student,
      status: studentWorkFiles.length > 0 ? 'matched' : 'unmatched' // 有文件时为已匹配，无文件时为未匹配
    };

    console.log('Save student work:', updatedStudent, studentWorkFiles);
    onSave(updatedStudent, studentWorkFiles);
    handleClose();
  };

  if (!student) return null;

  return (
    <MyModal
      isOpen={isOpen}
      onClose={handleClose}
      title=""
      w={vwDims(728)}
      h={vwDims(840)}
      maxW={vwDims(728)}
      maxH={vwDims(840)}
      minH={vwDims(840)}
      isCentered
      bg="#F7F8FC"
      sx={{
        '& .chakra-modal__content': {
          width: `${vwDims(728)} !important`,
          maxWidth: `${vwDims(728)} !important`,
          height: `${vwDims(840)} !important`,
          maxHeight: `${vwDims(840)} !important`,
          minHeight: `${vwDims(840)} !important`
        }
      }}
    >
      <Box p={0}>
        {/* 标题栏 */}
        <Flex
          align="center"
          justify="space-between"
          p={vwDims(20)}
          borderBottom="1px solid #E5E7EB"
        >
          <Text fontSize={vwDims(16)} fontWeight="600" color="#1A1A1A">
            学生{student.studentName}（{student.studentCode}）的作业
          </Text>
        </Flex>

        {/* 主要内容区域 */}
        <Box display="flex" flexDirection="column" h={vwDims(697)} py={vwDims(24)} px={vwDims(32)}>
          {studentWorkFiles.length === 0 ? (
            // 无文件状态 - 居中显示添加文件
            <Flex
              direction="column"
              align="center"
              justify="center"
              cursor={isUploading ? 'not-allowed' : 'pointer'}
              onClick={isUploading ? undefined : handleAddStudentWorkFile}
              opacity={isUploading ? 0.6 : 1}
              bg="white"
              borderRadius={'8px'}
              border="1px dashed #E5E6EB"
              h={vwDims(648)}
            >
              <Box
                w={vwDims(52)}
                h={vwDims(52)}
                borderRadius="50%"
                display="flex"
                alignItems="center"
                justifyContent="center"
                mb={vwDims(16)}
              >
                <SvgIcon name="uploadAdd" w={vwDims(52)} h={vwDims(52)} />
              </Box>
              <Text fontSize={vwDims(16)} color="#1A1A1A" fontWeight="500">
                {isUploading ? '上传中...' : '添加文件'}
              </Text>
            </Flex>
          ) : (
            // 有文件状态
            <Box
              display="flex"
              flexDirection="column"
              h="100%"
              py={vwDims(16)}
              px={vwDims(24)}
              bg={'#fff'}
              borderRadius={vwDims(12)}
            >
              {/* 提示信息 */}
              <Box pb={vwDims(12)}>
                <Text fontSize={vwDims(16)} color="#86909C" fontWeight={500}>
                  您可拖动调整文件先后顺序，确保其与实际内容顺序匹配
                </Text>
              </Box>

              {/* 文件预览区域 */}
              <Box
                flex="1"
                border="1px solid #E5E7EB"
                borderRadius={vwDims(8)}
                bg="#FAFBFC"
                mb={vwDims(16)}
                overflow="hidden"
                position="relative"
              >
                {studentWorkFiles.length > 0 && studentWorkFiles[selectedFileIndex] ? (
                  <>
                    {/* 文件预览标题 */}
                    <Box
                      position="absolute"
                      top={vwDims(16)}
                      left={vwDims(16)}
                      zIndex={2}
                      bg="rgba(255, 255, 255, 0.9)"
                      px={vwDims(8)}
                      py={vwDims(4)}
                      borderRadius={vwDims(4)}
                    >
                      <Text fontSize={vwDims(14)} fontWeight="500" color="#1A1A1A">
                        文件{selectedFileIndex + 1}: {studentWorkFiles[selectedFileIndex].name}
                      </Text>
                    </Box>

                    {/* 文件预览内容 */}
                    <MemoizedFilePreview file={studentWorkFiles[selectedFileIndex]} />
                  </>
                ) : (
                  <Box display="flex" alignItems="center" justifyContent="center" h="100%">
                    <Text fontSize={vwDims(16)} color="#86909C">
                      暂无文件
                    </Text>
                  </Box>
                )}
              </Box>

              {/* 底部文件缩略图列表 */}
              <Box>
                {/* 文件数量提示 */}
                <Flex justify="space-between" align="center" mb={vwDims(8)}>
                  <Text fontSize={vwDims(14)} fontWeight="500" color="#1A1A1A">
                    文件列表
                  </Text>
                  <Text fontSize={vwDims(12)} color="#86909C">
                    {studentWorkFiles.length}/3 个文件
                  </Text>
                </Flex>

                <Flex
                  gap={vwDims(12)}
                  align="center"
                  py={vwDims(8)}
                  px={vwDims(8)}
                  background={'#F9F8FC'}
                  borderRadius={'10px'}
                >
                  {studentWorkFiles.map((file, index) => (
                    <Box key={file.id} position="relative">
                      {/* 删除按钮 - 移到外层容器 */}
                      <Box
                        position="absolute"
                        top={vwDims(-8)}
                        right={vwDims(-14)}
                        w={vwDims(24)}
                        h={vwDims(24)}
                        borderRadius={'50%'}
                        overflow={'hidden'}
                        cursor="pointer"
                        zIndex={10}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemoveStudentWorkFile(file.id);
                        }}
                      >
                        <SvgIcon name="uploadedClose2" w={vwDims(24)} h={vwDims(24)} />
                      </Box>

                      <Box>
                        <Box
                          w={vwDims(194)}
                          h={vwDims(82)}
                          border={
                            index === selectedFileIndex ? '1px solid #7D4DFF' : '1px solid #E5E6EB'
                          }
                          borderRadius={vwDims(8)}
                          bg="white"
                          cursor="pointer"
                          _hover={{
                            borderColor: index === selectedFileIndex ? '#7D4DFF' : '#E5E6EB'
                          }}
                          transition="all 0.2s ease"
                          opacity={draggedIndex === index ? 0.5 : 1}
                          transform={draggedIndex === index ? 'scale(0.95)' : 'scale(1)'}
                          draggable
                          onClick={() => setSelectedFileIndex(index)}
                          onDragStart={(e) => handleDragStart(e, index)}
                          onDragEnd={handleDragEnd}
                          onDragOver={handleDragOver}
                          onDrop={(e) => handleDrop(e, index)}
                        >
                          {/* 文件缩略图 */}
                          <Box
                            w="100%"
                            h="100%"
                            bg="#F5F5F5"
                            borderRadius={vwDims(8)}
                            overflow="hidden"
                          >
                            <MemoizedSmallFilePreview file={file} />
                          </Box>
                        </Box>
                      </Box>
                    </Box>
                  ))}

                  {/* 继续添加按钮 - 只在文件数量少于3个时显示 */}
                  {studentWorkFiles.length < 3 && (
                    <Box
                      w={vwDims(194)}
                      h={vwDims(82)}
                      border="1px dashed #7D4DFF"
                      borderRadius={vwDims(8)}
                      bg="white"
                      display="flex"
                      flexDirection="column"
                      alignItems="center"
                      justifyContent="center"
                      _hover={isUploading ? {} : { bg: '#F0F7FF' }}
                      transition="all 0.2s ease"
                      cursor={isUploading ? 'not-allowed' : 'pointer'}
                      opacity={isUploading ? 0.6 : 1}
                      onClick={isUploading ? undefined : handleAddStudentWorkFile}
                    >
                      <SvgIcon name="uploadAdd" w={vwDims(24)} h={vwDims(24)} color="white" />

                      <Text fontSize={vwDims(12)} color="#1D2129" fontWeight="500">
                        {isUploading ? '上传中...' : '继续添加'}
                      </Text>
                    </Box>
                  )}
                </Flex>
              </Box>
            </Box>
          )}
        </Box>

        {/* 底部按钮 */}
        <Flex justify="flex-end" gap={vwDims(12)} p={vwDims(20)} borderTop="1px solid #E5E7EB">
          <Button variant="outline" onClick={handleClose} fontSize={vwDims(14)}>
            取消
          </Button>
          <Button colorScheme="purple" onClick={handleSave} fontSize={vwDims(14)}>
            保存
          </Button>
        </Flex>
      </Box>
    </MyModal>
  );
};

export default StudentWorkModal;

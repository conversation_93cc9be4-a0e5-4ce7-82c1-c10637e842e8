import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Box, Flex, Text, Button, VStack, HStack, Avatar, Input } from '@chakra-ui/react';
import { Upload, message } from 'antd';
import MyModal from '@/components/MyModal';
import SvgIcon from '@/components/SvgIcon';
import { uploadFileRecognizeStudentCode } from '@/api/file';
import { teacherBatchSubmitHomework } from '@/api/homeworkDetail';
import { StudentSubmissionRecord, PaperFile } from '@/types/api/homeworkDetail';
import { vwDims } from '@/utils/chakra';
import StudentWorkModal from './StudentWorkModal';
// 添加CSS动画样式
const spinKeyframes = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

// 将样式注入到页面
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = spinKeyframes;
  document.head.appendChild(style);
}

const { Dragger } = Upload;

// 组件Props接口
interface HomeworkHelpRecordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit?: (data: any) => void;
  homeworkId?: number;
  clazzId?: number;
  studentRecord?: StudentSubmissionRecord; // 学生记录数据
  paperFiles?: Array<{
    id: string;
    createTime: string;
    updateTime: string;
    isDeleted: number;
    submitId: number;
    questionId: number;
    fileKey: string;
  }>; // 学生已提交的文件
  studentList?: StudentSubmissionRecord[]; // 班级学生列表
  onRefresh?: () => void; // 刷新回调
}

// 上传文件数据结构
interface UploadedFile {
  id: string;
  name: string;
  url: string;
  fileKey?: string;
  status: 'uploading' | 'done' | 'error' | 'unmatched' | 'matched' | 'processing';
  type?: string;
  size: number;
  studentCode?: string; // 识别到的学号
  matchedStudent?: StudentMatch; // 匹配到的学生
}

// 学生匹配数据结构
interface StudentMatch {
  studentId: number;
  studentName: string;
  studentCode: string;
  avatarUrl?: string;
  content: string;
  status: 'matched' | 'unmatched';
  action: 'edit' | 'add';
  progress?: number;
}

const HomeworkHelpRecordModal: React.FC<HomeworkHelpRecordModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  homeworkId,
  clazzId,
  studentRecord,
  paperFiles = [],
  studentList = [],
  onRefresh
}) => {
  // 初始步骤状态
  const [currentStep, setCurrentStep] = useState<'upload' | 'match'>('upload');
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [selectedFileId, setSelectedFileId] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const [isLeftDragOver, setIsLeftDragOver] = useState(false);
  const [leftDragCounter, setLeftDragCounter] = useState(0);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showFilePreviewModal, setShowFilePreviewModal] = useState(false);
  const [selectedFileForPreview, setSelectedFileForPreview] = useState<UploadedFile | null>(null);
  const [studentCode, setStudentCode] = useState('');
  const [showStudentWorkModal, setShowStudentWorkModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<StudentMatch | null>(null);
  // 图片加载状态缓存，基于文件URL缓存加载状态
  const [imageLoadingStates, setImageLoadingStates] = useState<Record<string, boolean>>({});
  const [imageErrorStates, setImageErrorStates] = useState<Record<string, boolean>>({});

  // 使用传入的学生记录，转换为StudentMatch格式
  const [studentMatches, setStudentMatches] = useState<StudentMatch[]>([]);

  // 处理图片加载状态的函数
  const getImageLoadingState = (url: string): boolean => {
    // 如果从未加载过，返回true（加载中）
    // 如果已经加载过（成功或失败），返回false（不显示加载状态）
    return imageLoadingStates[url] ?? true;
  };

  const handleImageLoad = (url: string) => {
    setImageLoadingStates((prev) => ({ ...prev, [url]: false }));
  };

  const handleImageError = (url: string) => {
    setImageLoadingStates((prev) => ({ ...prev, [url]: false }));
    setImageErrorStates((prev) => ({ ...prev, [url]: true }));
  };

  const getImageErrorState = (url: string): boolean => {
    return imageErrorStates[url] ?? false;
  };

  // 判断是否为批量模式
  const isBatchMode = !studentRecord && studentList.length > 0;

  // 创建稳定的学生ID列表用于依赖比较
  const studentIds = useMemo(() => {
    return studentList.map((student) => student.studentId || 0).join(',');
  }, [studentList]);

  // 创建稳定的文件ID列表用于依赖比较
  const paperFileIds = useMemo(() => {
    return paperFiles.map((file) => file.id).join(',');
  }, [paperFiles]);

  // 初始化学生匹配数据
  useEffect(() => {
    if (studentRecord) {
      // 单个学生模式
      const currentStudent: StudentMatch = {
        studentId: studentRecord.studentId || 0,
        studentName: studentRecord.studentName || '',
        studentCode: studentRecord.code || '',
        content: '',
        status: 'unmatched' as const,
        action: 'edit' as const,
        progress: 0
      };
      setStudentMatches([currentStudent]);
    } else if (studentList.length > 0) {
      // 批量学生模式
      const matches = studentList.map((student) => ({
        studentId: student.studentId || 0,
        studentName: student.studentName || '',
        studentCode: student.code || '',
        content: '',
        status: 'unmatched' as const,
        action: 'edit' as const,
        progress: 0
      }));
      setStudentMatches(matches);
    }
  }, [studentRecord, studentIds]); // 使用稳定的studentIds作为依赖

  // 检查输入的学号是否存在
  const isStudentCodeValid = useMemo(() => {
    if (!studentCode.trim()) return true; // 空值时不显示错误
    return studentMatches.some((student) => student.studentCode === studentCode.trim());
  }, [studentCode, studentMatches]);

  // 根据paperFiles设置初始步骤
  useEffect(() => {
    setCurrentStep(paperFiles.length > 0 ? 'match' : 'upload');
  }, [paperFiles.length]);

  // 初始化已有文件
  useEffect(() => {
    if (paperFiles.length > 0) {
      const existingFiles: UploadedFile[] = paperFiles.map((file, index) => {
        // 根据fileKey构造文件URL，通常格式为 /api/file/download/{fileKey}
        const fileUrl = file.fileKey ? `/api/file/download/${file.fileKey}` : '';

        // 根据文件扩展名推断文件类型
        const fileName = `文件${index + 1}`;
        let fileType = 'application/pdf'; // 默认为PDF

        // 如果fileKey包含扩展名，可以推断文件类型
        if (file.fileKey) {
          const extension = file.fileKey.toLowerCase().split('.').pop();
          switch (extension) {
            case 'jpg':
            case 'jpeg':
              fileType = 'image/jpeg';
              break;
            case 'png':
              fileType = 'image/png';
              break;
            case 'pdf':
              fileType = 'application/pdf';
              break;
            default:
              fileType = 'application/octet-stream';
          }
        }

        return {
          id: file.id,
          name: fileName,
          url: fileUrl,
          fileKey: file.fileKey,
          status: 'done',
          type: fileType,
          size: 0
        };
      });
      setUploadedFiles(existingFiles);
      if (existingFiles.length > 0) {
        setSelectedFileId(existingFiles[0].id);
      }
    } else {
      // 如果没有已有文件，清空上传文件列表
      setUploadedFiles([]);
      setSelectedFileId('');
    }
  }, [paperFileIds]); // 使用稳定的paperFileIds作为依赖

  // 计算学生的实际匹配状态（基于实际的文件匹配情况）
  const getStudentActualStatus = useCallback(
    (student: StudentMatch): 'matched' | 'unmatched' => {
      const hasMatchedFiles = uploadedFiles.some(
        (file) => file.matchedStudent?.studentId === student.studentId && file.status === 'matched'
      );
      return hasMatchedFiles ? 'matched' : 'unmatched';
    },
    [uploadedFiles]
  );

  // 计算匹配统计（基于实际文件匹配情况）
  const matchStats = useMemo(() => {
    const unmatchedCount = studentMatches.filter(
      (student) => getStudentActualStatus(student) === 'unmatched'
    ).length;

    return {
      total: studentMatches.length,
      unmatched: unmatchedCount
    };
  }, [studentMatches, getStudentActualStatus]);

  // 文件上传处理 - 使用识别学号的API
  const handleFileUpload = useCallback(
    async (file: File) => {
      setIsUploading(true);

      // 创建临时文件对象，显示上传状态
      // 使用时间戳+随机字符串确保唯一性，避免批量上传时ID冲突
      const tempFileId = `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const tempFile: UploadedFile = {
        id: tempFileId,
        name: file.name,
        url: '',
        fileKey: '',
        status: 'uploading',
        type: file.type,
        size: file.size
      };

      // 立即添加到文件列表，显示上传状态
      setUploadedFiles((prev) => [...prev, tempFile]);

      try {
        // 创建FormData
        const formData = new FormData();
        formData.append('file', file);

        // 调用识别学号的上传API
        const result = await uploadFileRecognizeStudentCode(formData, {
          onUploadProgress: (progressEvent) => {
            console.log('Upload progress:', progressEvent);
          }
        });

        // 从返回结果中提取学号
        let recognizedStudentCode = '';

        // 首先检查直接的studentCode字段
        if (result.studentCode) {
          recognizedStudentCode = result.studentCode;
        } else if (result.fileJson) {
          // 如果没有直接字段，尝试从fileJson中解析
          try {
            const fileData = JSON.parse(result.fileJson);
            recognizedStudentCode = fileData.studentCode || '';
          } catch (e) {
            console.warn('解析fileJson失败:', e);
          }
        }

        // 更新临时文件对象
        const uploadedFile: UploadedFile = {
          id: tempFileId, // 使用临时文件的ID
          name: file.name,
          url: result.fileUrl,
          fileKey: result.fileKey,
          status: recognizedStudentCode ? 'matched' : 'unmatched',
          type: file.type,
          size: file.size,
          studentCode: recognizedStudentCode
        };

        // 如果识别到学号，尝试匹配学生
        if (recognizedStudentCode) {
          // 使用函数式更新来避免闭包问题
          setStudentMatches((prevStudents) => {
            // 在当前学生列表中查找匹配的学生
            const matchedStudent = prevStudents.find(
              (student) => student.studentCode === recognizedStudentCode
            );

            if (matchedStudent) {
              // 需要实时检查当前学生已匹配的文件数量
              setUploadedFiles((currentFiles) => {
                // 计算当前学生已匹配的文件数量（不包括正在上传的文件，只计算已完成的文件）
                const currentStudentFiles = currentFiles.filter(
                  (file) =>
                    file.matchedStudent?.studentId === matchedStudent.studentId &&
                    file.status !== 'uploading' // 排除正在上传的文件
                );

                // 检查是否超过限制（当前学生已有的文件数量）
                if (currentStudentFiles.length >= 3) {
                  uploadedFile.status = 'error';
                  uploadedFile.matchedStudent = matchedStudent;
                  console.log(`学生 ${matchedStudent.studentName} 的文件数量已达上限（3个）`);
                  message.warning(`学生 ${matchedStudent.studentName} 的文件数量已达上限（3个）`);
                } else {
                  uploadedFile.matchedStudent = matchedStudent;
                  uploadedFile.status = 'matched';
                  console.log(
                    `文件匹配成功：${recognizedStudentCode} -> ${matchedStudent.studentName}`
                  );
                }

                return currentFiles; // 返回原有文件列表
              });

              // 只有在文件成功匹配时才更新学生状态
              if (uploadedFile.status === 'matched') {
                // 更新对应学生的匹配状态
                return prevStudents.map((student) =>
                  student.studentCode === recognizedStudentCode
                    ? { ...student, status: 'matched' as const, action: 'edit' as const }
                    : student
                );
              }
            } else {
              uploadedFile.status = 'unmatched';
              console.log(`识别的学号 ${recognizedStudentCode} 在学生列表中未找到匹配`);
            }
            return prevStudents; // 没有匹配时或者超限时返回原数组
          });
        }

        // 如果没有识别到学号，但只有一个学生时，自动匹配给这个学生
        if (!recognizedStudentCode) {
          setStudentMatches((prevStudents) => {
            if (prevStudents.length === 1) {
              const singleStudent = prevStudents[0];

              // 在单个学生模式下，需要实时检查当前学生已匹配的文件数量
              setUploadedFiles((currentFiles) => {
                // 计算当前学生已匹配的文件数量（不包括正在上传的文件，只计算已完成的文件）
                const currentStudentFiles = currentFiles.filter(
                  (file) =>
                    file.matchedStudent?.studentId === singleStudent.studentId &&
                    file.status !== 'uploading' // 排除正在上传的文件
                );

                // 检查是否超过限制（当前学生已有的文件数量）
                if (currentStudentFiles.length >= 3) {
                  uploadedFile.status = 'error';
                  uploadedFile.matchedStudent = singleStudent;
                  console.log(`学生 ${singleStudent.studentName} 的文件数量已达上限（3个）`);
                  message.warning(`学生 ${singleStudent.studentName} 的文件数量已达上限（3个）`);
                } else {
                  uploadedFile.matchedStudent = singleStudent;
                  uploadedFile.status = 'matched';
                  console.log(`自动匹配到唯一学生：${singleStudent.studentName}`);
                }

                return currentFiles; // 返回原有文件列表
              });

              // 只有在文件成功匹配时才更新学生状态
              if (uploadedFile.status === 'matched') {
                // 更新学生的匹配状态
                return prevStudents.map((student) =>
                  student.studentId === singleStudent.studentId
                    ? { ...student, status: 'matched' as const, action: 'edit' as const }
                    : student
                );
              }
            }
            return prevStudents;
          });
        }

        // 更新临时文件为最终文件
        setUploadedFiles((prev) => prev.map((f) => (f.id === tempFileId ? uploadedFile : f)));
        setSelectedFileId(uploadedFile.id);
        setCurrentStep('match');

        // 根据匹配情况显示不同的成功消息
        if (uploadedFile.status === 'error') {
          // 数量超限的情况不显示成功消息，只显示警告
        } else if (recognizedStudentCode) {
          message.success(`文件上传成功，识别到学号：${recognizedStudentCode}`);
        } else if (uploadedFile.status === 'matched' && uploadedFile.matchedStudent) {
          message.success(
            `文件上传成功，已自动匹配到学生：${uploadedFile.matchedStudent.studentName}`
          );
        } else {
          message.success('文件上传成功，未识别到学号');
        }
      } catch (error) {
        console.error('File upload failed:', error);
        message.error('文件上传失败');

        // 移除临时文件
        setUploadedFiles((prev) => prev.filter((f) => f.id !== tempFileId));
      } finally {
        setIsUploading(false);
      }
    },
    [] // 移除依赖项，避免循环依赖
  );

  // 文件上传前验证
  const beforeUpload = (file: File) => {
    const isValidType = ['application/pdf', 'image/png', 'image/jpeg', 'image/jpg'].includes(
      file.type
    );
    if (!isValidType) {
      message.error('只支持 PDF、PNG、JPG 格式的文件');
      return false;
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过 10MB');
      return false;
    }

    // 移除预检查逻辑，允许所有文件上传，在匹配时进行数量控制
    handleFileUpload(file);
    return false; // 阻止默认上传行为
  };

  // 左侧文件列表拖拽事件处理
  const handleLeftDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setLeftDragCounter((prev) => {
      const newCount = prev + 1;
      if (newCount === 1) {
        setIsLeftDragOver(true);
      }
      return newCount;
    });
  };

  const handleLeftDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setLeftDragCounter((prev) => {
      const newCount = Math.max(0, prev - 1);
      if (newCount === 0) {
        setIsLeftDragOver(false);
      }
      return newCount;
    });
  };

  const handleLeftDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleLeftDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsLeftDragOver(false);
    setLeftDragCounter(0);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      files.forEach((file) => {
        beforeUpload(file);
      });
    }
  };

  // 删除文件
  const handleRemoveFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId));
    if (selectedFileId === fileId) {
      const remainingFiles = uploadedFiles.filter((f) => f.id !== fileId);
      if (remainingFiles.length > 0) {
        setSelectedFileId(remainingFiles[0].id);
      } else {
        setCurrentStep('upload');
        setSelectedFileId('');
      }
    }
  };

  // 获取状态背景色
  const getStatusBgColor = (status: string) => {
    switch (status) {
      case 'uploading':
        return '#F0F7FF'; // 上传中 - 蓝色背景
      case 'unmatched':
        return '#FFF7E8'; // 无匹配学号 - 橙色背景
      case 'matched':
        return '#E8FFEA'; // 已分配xxx - 绿色背景
      case 'processing':
        return '#F3EDFF'; // 处理中 - 紫色背景
      case 'error':
        return '#FFF3F3'; // 数量超限 - 红色背景
      case 'done':
        return '#F2F3F5'; // 未处理 - 灰色背景
      default:
        return '#F2F3F5';
    }
  };

  // 获取状态文字颜色
  const getStatusTextColor = (status: string) => {
    switch (status) {
      case 'uploading':
        return '#1890FF'; // 上传中 - 蓝色文字
      case 'unmatched':
        return '#FF7D00'; // 无匹配学号 - 橙色文字
      case 'matched':
        return '#00B42A'; // 已分配xxx - 绿色文字
      case 'processing':
        return '#8C00E9'; // 处理中 - 紫色文字
      case 'error':
        return '#F20F0F'; // 数量超限 - 红色文字
      case 'done':
        return '#4E5969'; // 未处理 - 灰色文字
      default:
        return '#4E5969';
    }
  };

  // 获取状态文字
  const getStatusText = (status: string, file?: UploadedFile) => {
    switch (status) {
      case 'uploading':
        return '上传中...';
      case 'unmatched':
        return '无匹配学号';
      case 'matched':
        // 根据匹配的学生返回分配信息
        if (file?.matchedStudent) {
          return `已分配至${file.matchedStudent.studentName}`;
        }
        return '已匹配';
      case 'processing':
        return '处理中';
      case 'error':
        return '数量超限';
      case 'done':
        return '未处理';
      default:
        return '未处理';
    }
  };

  // 文件预览组件
  const FilePreview: React.FC<{
    file: UploadedFile;
    imageLoading: boolean;
    imageError: boolean;
    onImageLoad: (url: string) => void;
    onImageError: (url: string) => void;
  }> = React.memo(({ file, imageLoading, imageError, onImageLoad, onImageError }) => {
    const isImage = file.type?.startsWith('image/');
    const isPDF = file.type === 'application/pdf';
    const isUploading = file.status === 'uploading';

    // 如果文件正在上传，显示上传状态
    if (isUploading) {
      return (
        <Box 
          w="100%" 
          h="100%" 
          position="relative" 
          bg="#F5F5F5" 
          borderTopRadius={vwDims(8)}
          pointerEvents="none" // 让点击事件穿透
        >
          <Box
            w="100%"
            h="100%"
            display="flex"
            alignItems="center"
            justifyContent="center"
            flexDirection="column"
            pointerEvents="none" // 让点击事件穿透
          >
            {/* 上传动画 */}
            <Box
              w={vwDims(20)}
              h={vwDims(20)}
              border="2px solid #E5E7EB"
              borderTop="2px solid #7D4DFF"
              borderRadius="50%"
              animation="spin 1s linear infinite"
              mb={vwDims(4)}
            />
            <Text fontSize={vwDims(8)} color="#86909C" pointerEvents="none">
              上传中...
            </Text>
          </Box>
        </Box>
      );
    }

    if (isImage && file.url && !imageError) {
      return (
        <Box w="100%" h="100%" position="relative" pointerEvents="none"> {/* 让点击事件穿透 */}
          {imageLoading && (
            <Box
              position="absolute"
              top="0"
              left="0"
              w="100%"
              h="100%"
              display="flex"
              alignItems="center"
              justifyContent="center"
              bg="#F5F5F5"
              flexDirection="column"
              pointerEvents="none" // 让点击事件穿透
            >
              {/* 加载动画 */}
              <Box
                w={vwDims(16)}
                h={vwDims(16)}
                border="2px solid #E5E7EB"
                borderTop="2px solid #7D4DFF"
                borderRadius="50%"
                animation="spin 1s linear infinite"
                mb={vwDims(4)}
              />
              <Text fontSize={vwDims(8)} color="#86909C" pointerEvents="none">
                图片加载中...
              </Text>
            </Box>
          )}
          <Box
            as="img"
            src={file.url}
            alt={file.name}
            w="100%"
            h="100%"
            objectFit="cover"
            borderTopRadius={vwDims(8)}
            onLoad={() => onImageLoad(file.url)}
            onError={() => onImageError(file.url)}
            display={imageLoading ? 'none' : 'block'}
            pointerEvents="none" // 让点击事件穿透
            userSelect="none" // 防止图片选择
          />
        </Box>
      );
    }

    if (isPDF && file.url) {
      return (
        <Box
          w="100%"
          h="100%"
          position="relative"
          bg="#F5F5F5"
          borderTopRadius={vwDims(8)}
          overflow="hidden"
          pointerEvents="none" // 让点击事件穿透
          sx={{
            // 强制隐藏所有滚动条
            '&, & *': {
              scrollbarWidth: 'none !important',
              msOverflowStyle: 'none !important',
              '&::-webkit-scrollbar': {
                display: 'none !important',
                width: '0 !important',
                height: '0 !important'
              }
            }
          }}
        >
          {/* PDF预览iframe - 小尺寸 */}
          <Box
            as="iframe"
            src={`${file.url}#toolbar=0&navpanes=0&scrollbar=0&statusbar=0&messages=0&page=1&view=Fit&zoom=page-fit`}
            w="100%"
            h="100%"
            border="none"
            borderTopRadius={vwDims(8)}
            onLoad={() => onImageLoad(file.url)}
            onError={() => onImageError(file.url)}
            display={imageLoading ? 'none' : 'block'}
            pointerEvents="none" // 禁用交互，只用于预览
            scrolling="no" // HTML属性禁用滚动
            style={{
              // 使用内联样式强制隐藏滚动条
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              overflow: 'hidden',
              border: 'none',
              outline: 'none'
            }}
            sx={{
              // 更强力的CSS隐藏滚动条
              '&': {
                scrollbarWidth: 'none !important',
                msOverflowStyle: 'none !important',
                overflow: 'hidden !important'
              },
              '&::-webkit-scrollbar': {
                display: 'none !important',
                width: '0 !important',
                height: '0 !important',
                background: 'transparent !important'
              },
              '&::-webkit-scrollbar-track': {
                display: 'none !important',
                background: 'transparent !important'
              },
              '&::-webkit-scrollbar-thumb': {
                display: 'none !important',
                background: 'transparent !important'
              },
              '&::-webkit-scrollbar-corner': {
                display: 'none !important',
                background: 'transparent !important'
              },
              // 针对iframe内容的全局样式
              '& *': {
                scrollbarWidth: 'none !important',
                msOverflowStyle: 'none !important',
                '&::-webkit-scrollbar': {
                  display: 'none !important'
                }
              }
            }}
          />

          {/* 加载状态 */}
          {imageLoading && (
            <Box
              position="absolute"
              top="0"
              left="0"
              w="100%"
              h="100%"
              display="flex"
              alignItems="center"
              justifyContent="center"
              bg="#F5F5F5"
              pointerEvents="none" // 让点击事件穿透
            >
              <VStack spacing={1} pointerEvents="none">
                <Text fontSize={vwDims(8)} color="#86909C" pointerEvents="none">
                  加载中...
                </Text>
              </VStack>
            </Box>
          )}

          {/* 错误状态 */}
          {imageError && (
            <VStack spacing={1} justify="center" h="100%" pointerEvents="none">
              <SvgIcon name="file" w={vwDims(24)} h={vwDims(24)} />
              <Text fontSize={vwDims(8)} color="#86909C" textAlign="center" pointerEvents="none">
                PDF
              </Text>
            </VStack>
          )}
        </Box>
      );
    }

    // 默认文件图标（包括图片加载失败的情况）
    return (
      <VStack spacing={1} justify="center" h="100%" pointerEvents="none"> {/* 让点击事件穿透 */}
        <SvgIcon name="file" w={vwDims(24)} h={vwDims(24)} />
        <Text fontSize={vwDims(8)} color="#86909C" textAlign="center" pointerEvents="none">
          {isImage ? '图片' : '文件'}
        </Text>
      </VStack>
    );
  });
  FilePreview.displayName = 'FilePreview';

  // 大尺寸文件预览组件（用于预览弹窗）
  const LargeFilePreview: React.FC<{
    file: UploadedFile;
    imageLoading: boolean;
    imageError: boolean;
    onImageLoad: (url: string) => void;
    onImageError: (url: string) => void;
  }> = React.memo(({ file, imageLoading, imageError, onImageLoad, onImageError }) => {
    const isImage = file.type?.startsWith('image/');
    const isPDF = file.type === 'application/pdf';
    const isUploading = file.status === 'uploading';

    // 如果文件正在上传，显示上传状态
    if (isUploading) {
      return (
        <Box w="100%" h="100%" position="relative" bg="#F5F5F5">
          <Box
            w="100%"
            h="100%"
            display="flex"
            alignItems="center"
            justifyContent="center"
            flexDirection="column"
          >
            {/* 大尺寸上传动画 */}
            <Box
              w={vwDims(48)}
              h={vwDims(48)}
              border="4px solid #E5E7EB"
              borderTop="4px solid #7D4DFF"
              borderRadius="50%"
              animation="spin 1s linear infinite"
              mb={vwDims(12)}
            />
            <Text fontSize={vwDims(18)} color="#86909C" fontWeight="500">
              文件上传中...
            </Text>
            <Text fontSize={vwDims(14)} color="#86909C" mt={vwDims(4)}>
              {file.name}
            </Text>
          </Box>
        </Box>
      );
    }

    if (isImage && file.url && !imageError) {
      return (
        <Box w="100%" h="100%" position="relative" bg="#F5F5F5">
          {imageLoading && (
            <Box
              position="absolute"
              top="0"
              left="0"
              w="100%"
              h="100%"
              display="flex"
              alignItems="center"
              justifyContent="center"
              bg="#F5F5F5"
              flexDirection="column"
            >
              {/* 大尺寸加载动画 */}
              <Box
                w={vwDims(32)}
                h={vwDims(32)}
                border="3px solid #E5E7EB"
                borderTop="3px solid #7D4DFF"
                borderRadius="50%"
                animation="spin 1s linear infinite"
                mb={vwDims(8)}
              />
              <Text fontSize={vwDims(16)} color="#86909C">
                图片加载中...
              </Text>
              <Text fontSize={vwDims(12)} color="#86909C" mt={vwDims(4)}>
                {file.name}
              </Text>
            </Box>
          )}
          <Box
            as="img"
            src={file.url}
            alt={file.name}
            maxW="100%"
            maxH="100%"
            objectFit="contain"
            borderRadius={vwDims(8)}
            onLoad={() => onImageLoad(file.url)}
            onError={() => onImageError(file.url)}
            display={imageLoading ? 'none' : 'block'}
            margin="auto"
          />
        </Box>
      );
    }

    if (isPDF && file.url) {
      return (
        <Box
          w="100%"
          h="100%"
          position="relative"
          bg="#F5F5F5"
          overflow="hidden"
          sx={{
            // 强制隐藏所有滚动条
            '&, & *': {
              scrollbarWidth: 'none !important',
              msOverflowStyle: 'none !important',
              '&::-webkit-scrollbar': {
                display: 'none !important',
                width: '0 !important',
                height: '0 !important'
              }
            }
          }}
        >
          {/* PDF预览iframe */}
          <Box
            as="iframe"
            src={`${file.url}#toolbar=0&navpanes=0&scrollbar=0&page=1&view=FitH&zoom=page-width`}
            w="100%"
            h="100%"
            border="none"
            borderRadius={vwDims(8)}
            onLoad={() => onImageLoad(file.url)}
            onError={() => onImageError(file.url)}
            display={imageLoading ? 'none' : 'block'}
            sx={{
              // 隐藏iframe内部的滚动条 - 更强力的隐藏
              '&::-webkit-scrollbar': {
                display: 'none !important',
                width: '0 !important',
                height: '0 !important'
              },
              '&::-webkit-scrollbar-track': {
                display: 'none !important'
              },
              '&::-webkit-scrollbar-thumb': {
                display: 'none !important'
              },
              scrollbarWidth: 'none !important', // Firefox
              msOverflowStyle: 'none !important', // IE/Edge
              overflow: 'hidden !important',
              // 针对iframe内容的样式
              '& iframe': {
                scrollbarWidth: 'none !important',
                msOverflowStyle: 'none !important',
                '&::-webkit-scrollbar': {
                  display: 'none !important'
                }
              }
            }}
          />

          {/* 加载状态 */}
          {imageLoading && (
            <Box
              position="absolute"
              top="0"
              left="0"
              w="100%"
              h="100%"
              display="flex"
              alignItems="center"
              justifyContent="center"
              bg="#F5F5F5"
            >
              <VStack spacing={2}>
                <Text fontSize={vwDims(16)} color="#86909C">
                  PDF加载中...
                </Text>
              </VStack>
            </Box>
          )}

          {/* 错误状态 */}
          {imageError && (
            <Box
              position="absolute"
              top="0"
              left="0"
              w="100%"
              h="100%"
              display="flex"
              alignItems="center"
              justifyContent="center"
              bg="#F5F5F5"
            >
              <VStack spacing={4}>
                <SvgIcon name="file" w={vwDims(48)} h={vwDims(48)} />
                <VStack spacing={2}>
                  <Text fontSize={vwDims(16)} color="#1A1A1A" fontWeight="500">
                    PDF 预览失败
                  </Text>
                  <Text fontSize={vwDims(14)} color="#86909C">
                    请下载文件查看完整内容
                  </Text>
                  <Button
                    size="sm"
                    colorScheme="purple"
                    onClick={() => handleDownloadFile(file)}
                    mt={2}
                  >
                    下载文件
                  </Button>
                </VStack>
              </VStack>
            </Box>
          )}
        </Box>
      );
    }

    // 默认文件图标（包括图片加载失败的情况）
    return (
      <VStack spacing={4} justify="center" h="100%">
        <SvgIcon name="file" w={vwDims(48)} h={vwDims(48)} />
        <VStack spacing={1}>
          <Text fontSize={vwDims(16)} color="#1A1A1A" fontWeight="500">
            {isImage ? '图片文件' : '文件'}
          </Text>
          <Text fontSize={vwDims(14)} color="#86909C">
            {imageError ? '图片加载失败' : '暂不支持预览此格式'}
          </Text>
        </VStack>
      </VStack>
    );
  });
  LargeFilePreview.displayName = 'LargeFilePreview';
  // 重置状态
  const handleClose = () => {
    setCurrentStep('upload');
    setUploadedFiles([]);
    setSelectedFileId('');
    setIsUploading(false);
    onClose();
  };

  // 显示确认弹窗
  const handleSubmitClick = () => {
    setShowConfirmModal(true);
  };

  // 确认提交
  const handleConfirmSubmit = async () => {
    try {
      // 检查是否有匹配的学生和文件
      const matchedFiles = uploadedFiles.filter(
        (file) => file.status === 'matched' && file.matchedStudent
      );

      if (matchedFiles.length === 0) {
        message.error('请至少匹配一个学生的作业文件');
        return;
      }

      if (isBatchMode) {
        // 批量模式：按学生ID分组文件，构造所有学生的提交请求
        const studentFilesMap = new Map<number, UploadedFile[]>();

        // 按 homeworkStudentId 分组文件
        matchedFiles
          .filter((file) => file.matchedStudent)
          .forEach((file) => {
            const studentId = file.matchedStudent!.studentId;
            if (!studentFilesMap.has(studentId)) {
              studentFilesMap.set(studentId, []);
            }
            studentFilesMap.get(studentId)!.push(file);
          });

        const studentHomeworkSubmitRequests = Array.from(studentFilesMap.entries()).map(
          ([studentId, files]) => {
            // 从studentList中找到对应的学生记录
            const studentRecord = studentList.find((s) => s.studentId === studentId);

            if (!studentRecord) {
              console.error('未找到学生记录, studentId:', studentId);
              throw new Error(`未找到学生记录: studentId ${studentId}`);
            }

            // 确保homeworkStudentId有效
            const homeworkStudentId = studentRecord.id ? parseInt(studentRecord.id) : null;
            if (!homeworkStudentId || homeworkStudentId <= 0) {
              console.error('学生作业ID无效:', studentRecord);
              throw new Error(`学生 ${studentRecord.studentName} 的作业ID无效`);
            }

            // 将该学生的所有文件合并到一个 paperFiles 数组中
            const paperFiles = files.map((file) => ({
              id: null,
              fileKey: file.fileKey || ''
            }));

            // 根据API文档，homeworkStudentId应该使用学生记录的id字段（作业学生ID）
            // id字段应该使用submitId（更新时必传）
            return {
              id: studentRecord.submitId || null,
              homeworkStudentId: homeworkStudentId,
              correctCount: 0,
              timeSecond: 0,
              status: 1, // 已提交
              paperFiles,
              questionsRequestList: []
            };
          }
        );

        const submitParams = {
          studentHomeworkSubmitRequests
        };

        console.log('批量提交参数:', submitParams);
        console.log(
          '学生记录映射:',
          studentHomeworkSubmitRequests.map((req) => {
            const studentRecord = studentList.find(
              (s) => s.id && parseInt(s.id) === req.homeworkStudentId
            );
            return {
              homeworkStudentId: req.homeworkStudentId,
              submitId: req.id,
              studentName: studentRecord?.studentName,
              studentCode: studentRecord?.code,
              originalStudentRecord: studentRecord
            };
          })
        );
        await teacherBatchSubmitHomework(submitParams);
        message.success(`成功为 ${matchedFiles.length} 名学生提交作业帮录`);
      } else {
        // 单个学生模式
        if (!studentRecord?.studentId) {
          message.error('无法获取学生信息');
          return;
        }

        const questionsRequestList: any[] = [];

        // 构造单个学生的纸质作业文件列表
        const paperFiles: PaperFile[] = matchedFiles.map((file) => ({
          id: null,
          fileKey: file.fileKey || ''
        }));

        const submitParams = {
          studentHomeworkSubmitRequests: [
            {
              id: studentRecord.submitId || null,
              homeworkStudentId: studentRecord.id
                ? parseInt(studentRecord.id)
                : studentRecord.studentId,
              correctCount: 0,
              timeSecond: 0,
              status: 1, // 已提交
              paperFiles,
              questionsRequestList
            }
          ]
        };

        console.log('单个学生提交参数:', submitParams);
        console.log('学生记录详情:', {
          originalStudentRecord: studentRecord,
          mappedId: studentRecord.submitId || null,
          mappedHomeworkStudentId: studentRecord.id
            ? parseInt(studentRecord.id)
            : studentRecord.studentId
        });
        await teacherBatchSubmitHomework(submitParams);
        message.success('作业帮录提交成功');
      }

      setShowConfirmModal(false);
      handleClose();

      // 刷新列表
      onRefresh?.();
    } catch (error) {
      console.error('提交失败:', error);
      message.error('作业帮录提交失败');
    }
  };

  // 取消确认
  const handleCancelConfirm = () => {
    setShowConfirmModal(false);
  };

  // 下载文件
  const handleDownloadFile = (file: UploadedFile) => {
    if (file.url) {
      const link = document.createElement('a');
      link.href = file.url;
      link.download = file.name;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // 打开文件预览弹窗
  const handleOpenFilePreview = (file: UploadedFile) => {
    setSelectedFileForPreview(file);
    // 如果文件已经有匹配的学号，预填充到输入框中
    setStudentCode(file.studentCode || file.matchedStudent?.studentCode || '');
    setShowFilePreviewModal(true);
  };

  // 关闭文件预览弹窗
  const handleCloseFilePreview = () => {
    setShowFilePreviewModal(false);
    setSelectedFileForPreview(null);
    setStudentCode('');
  };

  // 保存学号分配
  const handleSaveStudentCode = () => {
    if (!studentCode.trim()) {
      message.error('请输入学号');
      return;
    }

    if (!selectedFileForPreview) {
      message.error('未选择文件');
      return;
    }

    // 在学生列表中查找匹配的学生
    const matchedStudent = studentMatches.find(
      (student) => student.studentCode === studentCode.trim()
    );

    if (!matchedStudent) {
      message.error(`未找到学号为 ${studentCode.trim()} 的学生`);
      return;
    }

    // 检查该学生的文件数量是否已达到上限
    if (checkStudentFileLimit(matchedStudent.studentId)) {
      message.error(`学生 ${matchedStudent.studentName} 的文件数量已达上限（3个）`);
      return;
    }

    // 更新文件的匹配信息
    setUploadedFiles((prevFiles) =>
      prevFiles.map((file) =>
        file.id === selectedFileForPreview.id
          ? {
              ...file,
              status: 'matched' as const,
              studentCode: studentCode.trim(),
              matchedStudent: matchedStudent
            }
          : file
      )
    );

    // 更新学生的匹配状态
    setStudentMatches((prevStudents) =>
      prevStudents.map((student) =>
        student.studentCode === studentCode.trim()
          ? { ...student, status: 'matched' as const, action: 'edit' as const }
          : student
      )
    );

    console.log(
      `文件 ${selectedFileForPreview.name} 已分配给学生 ${matchedStudent.studentName} (${studentCode.trim()})`
    );
    message.success(`文件已分配给学生 ${matchedStudent.studentName}`);
    handleCloseFilePreview();
  };

  // 打开学生作业管理弹窗
  const handleOpenStudentWork = (student: StudentMatch) => {
    setSelectedStudent(student);
    setShowStudentWorkModal(true);
  };

  // 获取学生匹配的文件（按顺序排序）- 使用 useCallback 优化性能
  const getStudentFiles = useCallback(
    (student: StudentMatch | null): UploadedFile[] => {
      if (!student) return [];

      const studentFiles = uploadedFiles.filter(
        (file) => file.matchedStudent?.studentId === student.studentId
      );

      // 开发环境下的调试日志
      if (process.env.NODE_ENV === 'development') {
        console.log('Getting student files for:', student.studentName, studentFiles);
      }

      // 按sortOrder字段排序，如果没有sortOrder字段则保持原有顺序
      const sortedFiles = studentFiles.sort((a, b) => {
        const sortOrderA = (a as any).sortOrder ?? 0;
        const sortOrderB = (b as any).sortOrder ?? 0;
        return sortOrderA - sortOrderB;
      });

      // 开发环境下的调试日志
      if (process.env.NODE_ENV === 'development') {
        console.log(
          'Sorted student files:',
          sortedFiles.map((f, i) => ({ index: i, name: f.name, sortOrder: (f as any).sortOrder }))
        );
      }

      return sortedFiles;
    },
    [uploadedFiles]
  );

  // 缓存当前选中学生的文件列表，避免重复计算
  const selectedStudentFiles = useMemo(() => {
    return selectedStudent ? getStudentFiles(selectedStudent) : [];
  }, [selectedStudent, getStudentFiles]);

  /**
   * 堆叠文件缩略图组件
   */
  const StackedFileThumbnails: React.FC<{ student: StudentMatch }> = React.memo(({ student }) => {
    // 直接在 useMemo 内部处理文件过滤和排序逻辑，避免函数引用变化导致的缓存失效
    const studentFiles = useMemo(() => {
      if (!student) return [];

      const filteredFiles = uploadedFiles.filter(
        (file) => file.matchedStudent?.studentId === student.studentId
      );

      // 按sortOrder字段排序，如果没有sortOrder字段则保持原有顺序
      const sortedFiles = filteredFiles.sort((a, b) => {
        const sortOrderA = (a as any).sortOrder ?? 0;
        const sortOrderB = (b as any).sortOrder ?? 0;
        return sortOrderA - sortOrderB;
      });

      return sortedFiles;
    }, [student.studentId, uploadedFiles]);

    if (studentFiles.length === 0) {
      return (
        <Text fontSize={vwDims(12)} color="#86909C">
          —
        </Text>
      );
    }

    // 最多显示3个文件缩略图
    const displayFiles = studentFiles.slice(0, 3);

    return (
      <Box position="relative" w={vwDims(74)} h={vwDims(46)}>
        {displayFiles.map((file, index) => (
          <Box
            key={file.id}
            position="absolute"
            left={`${index * 12}px`}
            top="0"
            w={vwDims(50)}
            h={vwDims(46)}
            borderRadius="4px"
            border="1px solid #E5E7EB"
            overflow="hidden"
            bg="white"
            zIndex={index + 1}
            boxShadow="0 1px 3px rgba(0, 0, 0, 0.1)"
          >
            {file.url ? (
              file.type?.toLowerCase().includes('pdf') ? (
                // PDF文件显示PDF图标
                <Box
                  w="100%"
                  h="100%"
                  bg="#FFF6E6"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <SvgIcon name="filePdf" w={vwDims(45.8)} h={vwDims(40)} />
                </Box>
              ) : (
                // 图片文件显示缩略图
                <Box
                  w="100%"
                  h="100%"
                  backgroundImage={`url(${file.url})`}
                  backgroundSize="cover"
                  backgroundPosition="center"
                  backgroundRepeat="no-repeat"
                />
              )
            ) : (
              // 无URL时显示占位符
              <Box
                w="100%"
                h="100%"
                bg="#F5F5F5"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <Text fontSize={vwDims(8)} color="#999">
                  文件
                </Text>
              </Box>
            )}
          </Box>
        ))}
      </Box>
    );
  });

  // 优化 React.memo 比较函数，只在 studentId 变化时才重新渲染
  StackedFileThumbnails.displayName = 'StackedFileThumbnails';

  const MemoizedStackedFileThumbnails = React.memo(
    StackedFileThumbnails,
    (prevProps, nextProps) => {
      // 只在 studentId 变化时才重新渲染，忽略其他属性的变化
      return prevProps.student.studentId === nextProps.student.studentId;
    }
  );

  MemoizedStackedFileThumbnails.displayName = 'MemoizedStackedFileThumbnails';
  // 检查学生文件数量是否超限
  const checkStudentFileLimit = (studentId: number): boolean => {
    const studentFiles = uploadedFiles.filter(
      (file) => file.matchedStudent?.studentId === studentId
    );
    return studentFiles.length >= 3;
  };

  // 关闭学生作业管理弹窗
  const handleCloseStudentWork = () => {
    setShowStudentWorkModal(false);
    setSelectedStudent(null);
  };

  // 保存学生作业
  const handleSaveStudentWork = (student: StudentMatch, files: UploadedFile[]) => {
    console.log('Save student work:', student, files);
    console.log(
      'Files order from StudentWorkModal:',
      files.map((f, i) => ({ index: i, name: f.name, id: f.id }))
    );

    // 更新uploadedFiles状态：更新该学生的文件顺序和匹配关系
    setUploadedFiles((prevFiles) => {
      console.log('Previous files:', prevFiles);

      // 1. 移除该学生的所有旧文件
      const filesWithoutStudent = prevFiles.filter(
        (file) => file.matchedStudent?.studentId !== student.studentId
      );
      console.log('Files without current student:', filesWithoutStudent);

      // 2. 处理该学生的文件：保持新的顺序，添加时间戳确保顺序
      const currentTime = Date.now();
      const updatedStudentFiles = files.map((newFile, index) => ({
        ...newFile,
        // 使用时间戳 + 索引来确保顺序，这样可以避免类型问题
        sortOrder: currentTime + index
      }));
      console.log('Updated student files with sort order:', updatedStudentFiles);

      // 3. 将该学生的文件按新顺序添加回去
      const finalFiles = [...filesWithoutStudent, ...updatedStudentFiles];
      console.log('Final files array:', finalFiles);

      return finalFiles;
    });

    // 更新学生的匹配状态：根据文件数量决定是否匹配
    setStudentMatches((prevStudents) =>
      prevStudents.map((s) =>
        s.studentId === student.studentId
          ? {
              ...s,
              status: files.length > 0 ? 'matched' : 'unmatched', // 有文件时为已匹配，无文件时为未匹配
              action: 'edit' as const
            }
          : s
      )
    );

    handleCloseStudentWork();
  };

  // 处理StudentWorkModal中的文件上传
  const handleStudentFileUpload = (file: UploadedFile, student: StudentMatch) => {
    // 检查该学生的文件数量是否已达到上限
    if (checkStudentFileLimit(student.studentId)) {
      message.error(`学生 ${student.studentName} 的文件数量已达上限（3个）`);
      return;
    }

    // 添加文件到已上传文件列表
    setUploadedFiles((prevFiles) => [...prevFiles, file]);

    // 更新学生的匹配状态
    setStudentMatches((prevStudents) =>
      prevStudents.map((s) =>
        s.studentId === student.studentId
          ? { ...s, status: 'matched' as const, action: 'edit' as const }
          : s
      )
    );

    console.log(
      `StudentWorkModal中上传的文件已添加到主列表: ${file.name} -> ${student.studentName}`
    );
    message.success(`文件已上传并分配给学生 ${student.studentName}`);
  };

  // 渲染上传界面
  const renderUploadStep = () => (
    <Box h={vwDims(684)} padding={`${vwDims(23)} ${vwDims(32)} ${vwDims(0)} ${vwDims(32)}`}>
      <VStack h="100%">
        <Box
          display={'flex'}
          w={'100%'}
          fontWeight={600}
          fontSize={vwDims(16)}
          color="#86909C"
          textAlign={'left'}
        >
          <Text color={'#000'}>上传文件</Text>
          （上传学生作业文件，系统将尝试 <Text color={'#7D4DFF'}>自动匹配学号</Text>
          ，可以手动调整分配）
        </Box>

        <Box w={vwDims(996)} h={vwDims(568)}>
          <Dragger
            beforeUpload={beforeUpload}
            showUploadList={false}
            disabled={isUploading}
            openFileDialogOnClick={false}
            style={{
              border: '2px dashed #D9D9D9',
              borderRadius: '8px',
              backgroundColor: '#FAFBFC',
              display: 'flex',
              alignItems: 'center',
              // justifyContent: 'center',
              width: '100%',
              height: '100%'
            }}
          >
            <VStack spacing={6}>
              <Box
                w={vwDims(80)}
                h={vwDims(80)}
                border="2px solid #7D4DFF"
                borderRadius={vwDims(12)}
                display="flex"
                alignItems="center"
                justifyContent="center"
                position="relative"
              >
                <SvgIcon
                  name="HomeworkCorrectionUpload"
                  w={vwDims(126)}
                  h={vwDims(86)}
                  color="#7D4DFF"
                />
                <Box
                  position="absolute"
                  bottom={vwDims(-10)}
                  right={vwDims(-10)}
                  w={vwDims(28)}
                  h={vwDims(28)}
                  bg="#7D4DFF"
                  borderRadius="50%"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <SvgIcon name="add" w={vwDims(14)} h={vwDims(14)} color="white" />
                </Box>
              </Box>

              <Text fontSize={vwDims(18)} color="#1A1A1A" fontWeight="500">
                请上传作业文件，可拖拽文件到此区域
              </Text>

              <Text fontSize={vwDims(14)} color="#86909C">
                支持PDF、PNG、JPG
              </Text>

              <Button
                colorScheme="purple"
                size="md"
                isLoading={isUploading}
                loadingText="上传中..."
                onClick={(e) => {
                  e.stopPropagation(); // 阻止事件冒泡到 Dragger 组件
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.accept = '.pdf,.png,.jpg,.jpeg';
                  input.multiple = true; // 支持多选文件
                  input.onchange = (e) => {
                    const files = Array.from((e.target as HTMLInputElement).files || []);
                    files.forEach((file) => beforeUpload(file)); // 遍历处理每个文件
                  };
                  input.click();
                }}
              >
                选择文件
              </Button>

              <Text
                fontSize={vwDims(14)}
                color="#86909C"
                textAlign="center"
                maxW={vwDims(500)}
                lineHeight="1.6"
              >
                <Text color={'#7D4DFF'}>
                  请确保图片（或PDF的每一页）中均为单页正向作业内容，以保障批改结果
                </Text>
                <br />
                系统将基于中学对学号与姓名进行匹配对应学生
              </Text>
            </VStack>
          </Dragger>
        </Box>
      </VStack>
    </Box>
  );

  // 渲染匹配界面
  const renderMatchStep = () => (
    <Flex
      h={vwDims(684)}
      p={`${vwDims(23)} ${vwDims(18)} ${vwDims(27)} ${vwDims(18)}`}
      background={'#f6f7fb'}
      gap={vwDims(16)}
    >
      {/* 左侧文件列表 */}
      <Box
        w={vwDims(362)}
        borderRight="1px solid #E5E7EB"
        position="relative"
        onDragEnter={handleLeftDragEnter}
        onDragLeave={handleLeftDragLeave}
        onDragOver={handleLeftDragOver}
        onDrop={handleLeftDrop}
        background={'#FFF'}
        borderRadius={8}
        transition="all 0.2s ease"
        px={vwDims(16)}
        py={vwDims(12)}
        height={'100%'}
        overflowY={'auto'}
      >
        <Text fontSize={vwDims(16)} fontWeight="600" color="#1A1A1A" mb={4}>
          已上传文件
        </Text>

        {/* 拖拽覆盖层 */}
        {isLeftDragOver && (
          <Box
            position="absolute"
            top="0"
            left="0"
            right="0"
            bottom="0"
            bg="rgba(59, 59, 59, 0.50)"
            borderRadius="8px"
            display="flex"
            alignItems="center"
            justifyContent="center"
            zIndex={10}
            pointerEvents="none"
          >
            <VStack spacing={2}>
              <Text fontSize={vwDims(18)} color="#FFFFFF" fontWeight="500">
                拖拽文件至此处
              </Text>
            </VStack>
          </Box>
        )}

        {/* 文件网格布局 */}
        <Box position="relative" zIndex={2} pointerEvents="auto">
          <Box display="grid" gridTemplateColumns="repeat(3, 1fr)" gap={vwDims(12)} mb={vwDims(12)}>
            {uploadedFiles.map((file) => (
              <Box key={file.id} w={vwDims(104)} h={vwDims(120)} position="relative">
                {/* 删除按钮 */}
                <Box
                  className="delete-button"
                  position="absolute"
                  top={vwDims(-6)}
                  right={vwDims(-6)}
                  w={vwDims(20)}
                  h={vwDims(20)}
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  cursor="pointer"
                  zIndex={10}
                  opacity={1}
                  visibility="visible"
                  transition="all 0.2s ease"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveFile(file.id);
                  }}
                >
                  <SvgIcon name="uploadedClose2" w={vwDims(20)} h={vwDims(20)} />
                </Box>
                {/* 文件卡片 */}
                <Box
                  position="relative"
                  border="1px solid #D1D5DB"
                  borderRadius={vwDims(8)}
                  bg="white"
                  w={vwDims(104)}
                  h={vwDims(120)}
                  cursor="pointer"
                  onClick={(e) => {
                    // 确保点击事件能正确处理
                    e.stopPropagation();
                    setSelectedFileId(file.id);
                    handleOpenFilePreview(file);
                  }}
                  _hover={{
                    borderColor: '#B0B0B0'
                  }}
                  display="flex"
                  flexDirection="column"
                  overflow={'hidden'}
                  // 确保所有子元素的点击都能触发预览
                  style={{
                    userSelect: 'none' // 防止文本选择影响点击
                  }}
                >
                  {/* 预览图区域 */}
                  <Box
                    position={'relative'}
                    h={vwDims(94)}
                    bg="#F5F5F5"
                    borderBottom="1px solid #E5E7EB"
                    flex="0 0 auto"
                    overflow="hidden"
                    borderTopRadius={vwDims(8)}
                    // 确保点击事件能向上传播
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedFileId(file.id);
                      handleOpenFilePreview(file);
                    }}
                    cursor="pointer"
                  >
                    <FilePreview
                      file={file}
                      imageLoading={getImageLoadingState(file.url)}
                      imageError={getImageErrorState(file.url)}
                      onImageLoad={handleImageLoad}
                      onImageError={handleImageError}
                    />
                    {/* 文件名 */}
                    <Text
                      position={'absolute'}
                      top={vwDims(60)}
                      left={'50%'}
                      transform={'translateX(-50%)'}
                      fontSize={vwDims(8)}
                      color="#1A1A1A"
                      fontWeight="500"
                      noOfLines={1}
                      textAlign="center"
                      mb={vwDims(1)}
                      pointerEvents="none" // 让点击事件穿透到父元素
                      userSelect="none" // 防止文本选择
                    >
                      {file.name}
                    </Text>
                  </Box>

                  {/* 文件信息 */}
                  <Box
                    h={vwDims(26)}
                    p={vwDims(4)}
                    flex="1"
                    display="flex"
                    flexDirection="column"
                    justifyContent="center"
                    minH={0}
                    bg={getStatusBgColor(file.status)}
                  >
                    {/* 状态标签 */}
                    <Text
                      fontSize={vwDims(8)}
                      fontWeight="400"
                      color={getStatusTextColor(file.status)}
                      textAlign="center"
                      lineHeight="1"
                      pointerEvents="none" // 让点击事件穿透到父元素
                      userSelect="none" // 防止文本选择
                    >
                      {getStatusText(file.status, file)}
                    </Text>
                  </Box>
                </Box>
              </Box>
            ))}

            {/* 继续添加文件卡片 */}
            {uploadedFiles.length > 0 && (
              <Box
                w={vwDims(104)} // 固定宽度 104px
                h={vwDims(120)} // 固定高度 120px
                borderRadius={vwDims(8)} // 圆角 8px
                border="1px dashed #E5E6EB" // 1px 虚线边框
                bg="#F9F8FC" // 背景色
                cursor="pointer"
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent="center"
                _hover={{
                  borderColor: '#7D4DFF',
                  bg: '#F0F7FF'
                }}
                transition="all 0.2s ease"
                onClick={() => {
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.accept = '.pdf,.png,.jpg,.jpeg';
                  input.multiple = true;
                  input.onchange = (e) => {
                    const files = Array.from((e.target as HTMLInputElement).files || []);
                    files.forEach((file) => beforeUpload(file));
                  };
                  input.click();
                }}
              >
                <Box
                  w={vwDims(24)}
                  h={vwDims(24)}
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  mb={vwDims(8)}
                >
                  <SvgIcon name="uploadAdd" w={vwDims(24)} h={vwDims(24)} />
                </Box>
                <Text fontSize={vwDims(12)} color="#1D2129" fontWeight="400">
                  继续添加
                </Text>
              </Box>
            )}
          </Box>
        </Box>

        {/* 拖拽提示区域 */}
        {uploadedFiles.length === 0 && (
          <Box
            p={6}
            border="2px dashed #D9D9D9"
            borderRadius="8px"
            bg="#FAFBFC"
            textAlign="center"
            color="#86909C"
            fontSize="14px"
            _hover={{
              borderColor: '#7D4DFF',
              color: '#7D4DFF',
              bg: '#F0F7FF'
            }}
            transition="all 0.2s ease"
          >
            <VStack spacing={2}>
              <SvgIcon name="upload" w="24px" h="24px" color="#86909C" />
              <Text>拖拽文件到此处上传</Text>
              <Text fontSize="12px">支持 PDF、PNG、JPG</Text>
            </VStack>
          </Box>
        )}
      </Box>

      {/* 右侧学生匹配结果 */}
      <Box flex="1" background={'#FFF'} borderRadius={8} padding={`${vwDims(12)} ${vwDims(16)}`}>
        <Flex justify="space-between" align="center" mb={4}>
          <Text fontSize={vwDims(16)} fontWeight="600" color="#1A1A1A">
            学生作业匹配结果
          </Text>
        </Flex>

        {/* 表格头部 */}
        <Box bg="#F8F9FA" borderRadius="8px 8px 0 0" border="1px solid #E5E7EB">
          <Flex align="center" h={vwDims(40)} px={4}>
            <Box w={vwDims(170)}>
              <Text fontSize={vwDims(14)} fontWeight="600" color="#1A1A1A">
                学生
              </Text>
            </Box>
            <Box w={vwDims(190)} textAlign="center">
              <Text fontSize={vwDims(14)} fontWeight="600" color="#1A1A1A">
                作业内容
              </Text>
            </Box>
            <Box w={vwDims(100)} textAlign="center">
              <Text fontSize={vwDims(14)} fontWeight="600" color="#1A1A1A">
                操作
              </Text>
            </Box>
            <Box w={vwDims(150)} textAlign="center" display={'flex'}>
              <Text fontSize={vwDims(14)} fontWeight="600" color="#1A1A1A">
                状态
              </Text>
              <Text
                color={'#86909c'}
                fontSize={vwDims(14)}
                fontWeight={400}
                marginLeft={vwDims(16)}
              >
                {matchStats.unmatched}/{matchStats.total} 未匹配
              </Text>
            </Box>
          </Flex>
        </Box>

        {/* 表格内容 */}
        <Box
          border="1px solid #E5E7EB"
          borderTop="none"
          borderRadius="0 0 8px 8px"
          maxH={vwDims(520)}
          overflowY="auto"
        >
          {studentMatches.map((student, index) => (
            <Flex
              key={student.studentId}
              align="center"
              h={vwDims(64)}
              px={4}
              borderBottom={index < studentMatches.length - 1 ? '1px solid #F0F0F0' : 'none'}
              _hover={{ bg: '#F8F9FA' }}
            >
              {/* 学生信息 */}
              <Box w={vwDims(170)}>
                <Flex align="center" gap={vwDims(8)}>
                  <SvgIcon name="columnsStudentIcon" w={vwDims(33)} h={vwDims(33)} />
                  <Box>
                    <Text fontSize={vwDims(14)} color="#86909C" lineHeight="1.2">
                      {student.studentCode}
                    </Text>
                    <Text fontSize={vwDims(14)} color="#1A1A1A" fontWeight="500" lineHeight="1.2">
                      {student.studentName}
                    </Text>
                  </Box>
                </Flex>
              </Box>

              {/* 作业内容 */}
              <Box w={vwDims(190)} display="flex" justifyContent="center" alignItems="center">
                <MemoizedStackedFileThumbnails student={student} />
              </Box>

              {/* 操作按钮 */}
              <Box w={vwDims(100)} textAlign="center">
                <Button
                  size="sm"
                  variant="link"
                  color="#7D4DFF"
                  fontSize={vwDims(14)}
                  fontWeight="400"
                  onClick={() => handleOpenStudentWork(student)}
                >
                  {getStudentActualStatus(student) === 'matched' ? '编辑' : '添加'}
                </Button>
              </Box>

              {/* 状态 */}
              <Box w={vwDims(150)}>
                <Box w={vwDims(72)} h={vwDims(26)} textAlign="center">
                  <Flex
                    align="center"
                    justify="center"
                    backgroundColor={
                      getStudentActualStatus(student) === 'matched' ? '#E8FFEA' : '#F2F3F5'
                    }
                    borderRadius={50}
                  >
                    <Box
                      w={vwDims(6)}
                      h={vwDims(6)}
                      bg={getStudentActualStatus(student) === 'matched' ? '#00B42A' : '#86909C'}
                      borderRadius="50%"
                      mr={1}
                    />
                    <Text
                      fontSize={vwDims(14)}
                      color={getStudentActualStatus(student) === 'matched' ? '#00B42A' : '#4E5969'}
                    >
                      {getStudentActualStatus(student) === 'matched' ? '已匹配' : '未匹配'}
                    </Text>
                  </Flex>
                </Box>
              </Box>
            </Flex>
          ))}
        </Box>
      </Box>
    </Flex>
  );

  // 获取未匹配学生列表（基于实际文件匹配情况）
  const getUnmatchedStudents = () => {
    return studentMatches.filter((student) => getStudentActualStatus(student) === 'unmatched');
  };

  // 渲染确认弹窗
  const renderConfirmModal = () => {
    const unmatchedStudents = getUnmatchedStudents();

    return (
      <MyModal
        isOpen={showConfirmModal}
        onClose={handleCancelConfirm}
        title=""
        w={vwDims(480)}
        h="auto"
        maxW={vwDims(480)}
        isCentered
      >
        <Box p={6}>
          {/* 标题区域 */}
          <Flex align="center" mb={4}>
            <Box
              w={vwDims(24)}
              h={vwDims(24)}
              bg="#FF4D4F"
              borderRadius="50%"
              display="flex"
              alignItems="center"
              justifyContent="center"
              mr={3}
            >
              <Text fontSize={vwDims(14)} color="white" fontWeight="bold">
                !
              </Text>
            </Box>
            <Text fontSize={vwDims(16)} fontWeight="600" color="#1A1A1A">
              确认提交任务
            </Text>
          </Flex>

          {/* 内容区域 */}
          <Box mb={6}>
            {isBatchMode ? (
              // 批量模式显示
              (() => {
                const unmatchedStudents = studentMatches.filter(
                  (student) =>
                    !uploadedFiles.some(
                      (file) => file.matchedStudent?.studentId === student.studentId
                    )
                );

                if (unmatchedStudents.length > 0) {
                  return (
                    <>
                      <Text fontSize={vwDims(14)} color="#1A1A1A" mb={3}>
                        以下{unmatchedStudents.length}位学生尚未匹配任何作业文件：
                      </Text>

                      <Box
                        bg="#F0F9FF"
                        borderRadius={vwDims(8)}
                        p={4}
                        mb={4}
                        overflowY="auto"
                        display={'flex'}
                        flexWrap={'wrap'}
                      >
                        {unmatchedStudents.map((student, index) => (
                          <Text
                            key={student.studentId}
                            fontSize={vwDims(12)}
                            color="#1A1A1A"
                            lineHeight="1.4"
                            whiteSpace={'nowrap'}
                          >
                            {student.studentName}
                            {index < unmatchedStudents.length - 1 ? '、' : ''}
                          </Text>
                        ))}
                      </Box>

                      <Text fontSize={vwDims(14)} color="#1A1A1A">
                        提交后这些学生将不会被批改。是否确认提交？
                      </Text>
                    </>
                  );
                } else {
                  return (
                    <>
                      {/* 这里是批量提交的提示 */}
                      <Text fontSize={vwDims(14)} color="#52C41A" fontWeight="500">
                        所有学生已匹配完毕，是否确认提交？
                      </Text>
                    </>
                  );
                }
              })()
            ) : // 单个学生模式显示
            unmatchedStudents.length > 0 ? (
              <>
                <Text fontSize={vwDims(14)} color="#1A1A1A" mb={3}>
                  以下1位学生尚未匹配任何作业文件：
                </Text>

                <Box
                  bg="#F0F9FF"
                  borderRadius={vwDims(8)}
                  p={4}
                  mb={4}
                  maxH={vwDims(200)}
                  overflowY="auto"
                  display={'flex'}
                >
                  <Text fontSize={vwDims(12)} color="#1A1A1A" lineHeight="1.4">
                    {studentRecord?.studentName || '未知学生'}
                  </Text>
                </Box>

                <Text fontSize={vwDims(14)} color="#1A1A1A">
                  提交后这些学生将不会被批改。是否确认提交？
                </Text>
              </>
            ) : (
              <>
                {/* 这里是单个提交的提示 */}
                <Text fontSize={vwDims(14)} color="#52C41A" fontWeight="500">
                  学生已匹配完毕，是否确认提交？
                </Text>
              </>
            )}
          </Box>

          {/* 按钮区域 */}
          <Flex justify="flex-end" gap={3}>
            <Button variant="outline" onClick={handleCancelConfirm} fontSize={vwDims(14)}>
              返回修改
            </Button>
            <Button colorScheme="purple" onClick={handleConfirmSubmit} fontSize={vwDims(14)}>
              确认提交
            </Button>
          </Flex>
        </Box>
      </MyModal>
    );
  };

  return (
    <>
      {/* 确认弹窗 */}
      {renderConfirmModal()}

      {/* 文件预览弹窗 */}
      {showFilePreviewModal && selectedFileForPreview && (
        <MyModal
          isOpen={showFilePreviewModal}
          onClose={handleCloseFilePreview}
          title=""
          w={vwDims(680)}
          h={vwDims(840)}
          maxH="90vh"
          isCentered
        >
          <Box
            p={0}
            h="100%"
            display="flex"
            flexDirection="column"
            sx={{
              // 自定义滚动条样式
              '&::-webkit-scrollbar': {
                width: '8px'
              },
              '&::-webkit-scrollbar-track': {
                background: '#f1f1f1',
                borderRadius: '4px'
              },
              '&::-webkit-scrollbar-thumb': {
                background: '#c1c1c1',
                borderRadius: '4px'
              },
              '&::-webkit-scrollbar-thumb:hover': {
                background: '#a8a8a8'
              }
            }}
          >
            {/* 标题栏 - 固定不滚动 */}
            <Flex
              align="center"
              justify="space-between"
              p={vwDims(20)}
              borderBottom="1px solid #E5E7EB"
              flexShrink={0}
            >
              <Flex align="center" gap={vwDims(12)}>
                <Text fontSize={vwDims(16)} fontWeight="600" color="#1A1A1A">
                  {selectedFileForPreview.name}
                </Text>
                {/* 状态标签 */}
                <Box
                  px={vwDims(8)}
                  py={vwDims(4)}
                  borderRadius={vwDims(4)}
                  bg={getStatusBgColor(selectedFileForPreview.status)}
                  color={getStatusTextColor(selectedFileForPreview.status)}
                  fontSize={vwDims(12)}
                  fontWeight="500"
                >
                  {getStatusText(selectedFileForPreview.status, selectedFileForPreview)}
                </Box>
              </Flex>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCloseFilePreview}
                p={0}
                minW="auto"
                h="auto"
              >
                {/* <SvgIcon name="close" w={vwDims(16)} h={vwDims(16)} color="#86909C" /> */}
              </Button>
            </Flex>

            {/* 可滚动的内容区域 */}
            <Box
              flex={1}
              overflowY="auto"
              onWheel={(e) => {
                // 确保滚轮事件可以正常工作
                e.stopPropagation();
              }}
              style={{
                // 确保可以接收滚轮事件
                pointerEvents: 'auto',
                touchAction: 'pan-y'
              }}
              sx={{
                // 自定义滚动条样式
                '&::-webkit-scrollbar': {
                  width: '8px'
                },
                '&::-webkit-scrollbar-track': {
                  background: '#f1f1f1',
                  borderRadius: '4px'
                },
                '&::-webkit-scrollbar-thumb': {
                  background: '#c1c1c1',
                  borderRadius: '4px'
                },
                '&::-webkit-scrollbar-thumb:hover': {
                  background: '#a8a8a8'
                },
                // 确保滚动行为正常
                scrollBehavior: 'smooth',
                overscrollBehavior: 'contain'
              }}
            >
              {/* 预览区域 */}
              <Box
                h={vwDims(480)}
                m={vwDims(20)}
                borderRadius={vwDims(8)}
                border="1px solid #E5E7EB"
                overflow="hidden"
              >
                <LargeFilePreview
                  file={selectedFileForPreview}
                  imageLoading={getImageLoadingState(selectedFileForPreview.url)}
                  imageError={getImageErrorState(selectedFileForPreview.url)}
                  onImageLoad={handleImageLoad}
                  onImageError={handleImageError}
                />
              </Box>

              {/* 学号输入区域 */}
              <Box px={vwDims(20)} pb={vwDims(20)}>
                <Box
                  display={'flex'}
                  alignItems={'center'}
                  gap={vwDims(8)}
                  h={vwDims(22)}
                  mb={vwDims(8)}
                >
                  <Text fontSize={vwDims(14)} fontWeight="500" color="#1A1A1A">
                    学号
                  </Text>
                  {/* 错误提示 - 根据不同状态显示 */}
                  {selectedFileForPreview.status === 'error' && (
                    <Text
                      display={'flex'}
                      alignItems={'center'}
                      gap={vwDims(4)}
                      fontSize={vwDims(14)}
                      color="#F20F0F"
                      ml={'auto'}
                    >
                      <SvgIcon name="ErrorWarning" w={vwDims(12)} h={vwDims(12)} color="#F20F0F" />
                      此学号对应图片数量超出最大限制，请修改学号或删除该生部分图片后再添加
                    </Text>
                  )}

                  {!isStudentCodeValid && (
                    <Text
                      display={'flex'}
                      alignItems={'center'}
                      gap={vwDims(4)}
                      fontSize={vwDims(14)}
                      color="#F20F0F"
                      ml={'auto'}
                    >
                      <SvgIcon name="ErrorWarning" w={vwDims(12)} h={vwDims(12)} color="#F20F0F" />
                      此学号不存在
                    </Text>
                  )}
                </Box>
                <Input
                  placeholder="请输入文件对应的学生学号"
                  value={studentCode}
                  onChange={(e) => setStudentCode(e.target.value)}
                  size="md"
                  fontSize={vwDims(14)}
                  bg="white"
                  border="1px solid #D1D5DB"
                  _focus={{
                    borderColor: '#7D4DFF',
                    boxShadow: '0 0 0 1px #7D4DFF'
                  }}
                />
              </Box>
            </Box>

            {/* 底部按钮 - 固定不滚动 */}
            <Flex
              justify="flex-end"
              gap={vwDims(12)}
              p={vwDims(20)}
              borderTop="1px solid #E5E7EB"
              flexShrink={0}
              bg="white"
            >
              <Button variant="outline" onClick={handleCloseFilePreview} fontSize={vwDims(14)}>
                取消
              </Button>
              <Button
                colorScheme="purple"
                onClick={handleSaveStudentCode}
                fontSize={vwDims(14)}
                isDisabled={!isStudentCodeValid || !studentCode.trim()}
              >
                保存
              </Button>
            </Flex>
          </Box>
        </MyModal>
      )}

      {/* 学生作业管理弹窗 */}
      <StudentWorkModal
        isOpen={showStudentWorkModal}
        onClose={handleCloseStudentWork}
        onSave={handleSaveStudentWork}
        student={selectedStudent}
        files={selectedStudentFiles}
        onFileUpload={handleStudentFileUpload}
      />

      {/* 主弹窗 */}
      <MyModal
        isOpen={isOpen}
        onClose={handleClose}
        title="作业帮录"
        w={vwDims(1092)}
        h={vwDims(840)}
        maxW={vwDims(1092)}
        maxH={vwDims(840)}
        isCentered
      >
        <Box>
          <Box>{currentStep === 'upload' ? renderUploadStep() : renderMatchStep()}</Box>

          <Box
            h={vwDims(78)}
            borderTop="1px solid #E5E7EB"
            display="flex"
            alignItems="center"
            justifyContent="flex-end"
            px={6}
          >
            <HStack spacing={3}>
              <Button variant="outline" onClick={handleClose}>
                取消
              </Button>
              <Button
                colorScheme="purple"
                onClick={handleSubmitClick}
                isDisabled={uploadedFiles.length === 0}
              >
                提交任务
              </Button>
            </HStack>
          </Box>
        </Box>
      </MyModal>
    </>
  );
};

export default HomeworkHelpRecordModal;
